package com.dpw.ctms.move.service.document.generator;

import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.dto.PaginationDTO;
import com.dpw.ctms.move.util.DateTimeUtil;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Vendor;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.dto.oms.ConsignmentListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.FacilityListRequestDTO;
import com.dpw.ctms.move.integration.dto.resource.UomListRequestDTO;
import com.dpw.ctms.move.integration.mapper.oms.OmsConsignmentMapper;
import com.dpw.ctms.move.integration.mapper.resource.ResourceFacilityMapper;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.FacilityDetailsDTO;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.dto.ProductDetailsDTO;
import com.dpw.ctms.move.dto.document.BOLDocumentDataDTO;
import com.dpw.ctms.move.dto.document.TripDocumentDTO;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.service.document.DocumentGenerator;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.ctms.move.constants.ErrorMessageConstant;
import com.dpw.tmsutils.async.ContextAwarePoolExecutor;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.bazaarvoice.jolt.Chainr;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.JOLT_SPECS_INVALID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.ERR_REQUEST_NOT_PROCESSED;

@Service
@RequiredArgsConstructor
@Slf4j
public class BOLDocumentGenerator implements DocumentGenerator<BOLDocumentDataDTO> {

    private final ITripDataService tripDataService;
    private final OmsServiceAdapter omsServiceAdapter;
    private final ResourceServiceAdapter resourceServiceAdapter;
    private final OmsConsignmentMapper consignmentMapper;
    private final ResourceFacilityMapper facilityMapper;
    private final ContextAwarePoolExecutor executor;
    private final ConfigService configService;

    @Override
    public DocumentType getDocumentType() {
        return DocumentType.BOL;
    }

    @Override
    public String generateJson(String tripCode, Vendor vendor) {
        log.info("Starting JSON generation for tripCode: {}, vendor: {}", tripCode, vendor);
        
        try {
            // Generate document context
            DocumentGenerationContextDTO<BOLDocumentDataDTO> context = generateContext(tripCode, vendor).get();

            // Get and validate document configuration (templateId + JOLT config)
            JsonNode documentConfig = getValidatedDocumentConfig(vendor);
            JsonNode joltConfig = documentConfig.get("joltConfig");
            List<Object> joltSpecList = JsonUtils.fromJsonNode(joltConfig, new TypeReference<List<Object>>() {});

            // Create JOLT transformer
            Chainr chainr = createJoltTransformer(joltSpecList, tripCode, vendor);

            // Transform document data
            String transformedJson = performJoltTransformation(chainr, context.getDocumentData(), tripCode);

            log.info("Successfully generated JSON for tripCode: {}", tripCode);
            return transformedJson;

        } catch (Exception e) {
            return handleJsonGenerationError(e, tripCode, vendor);
        }
    }

    private JsonNode getValidatedDocumentConfig(Vendor vendor) {
        JsonNode documentConfig = configService.getConfig(ConfigConstants.BOL_CONFIG, vendor);
        if (documentConfig == null || documentConfig.get("joltConfig") == null) {
            throw new TMSException(TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format(JOLT_SPECS_INVALID, vendor));
        }
        return documentConfig;
    }

    private Chainr createJoltTransformer(List<Object> joltSpecList, String tripCode, Vendor vendor) {
        if (joltSpecList == null || joltSpecList.isEmpty()) {
            throw new TMSException(TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format(JOLT_SPECS_INVALID, vendor));
        }

        try {
            Chainr chainr = Chainr.fromSpec(joltSpecList);
            if (chainr == null) {
                throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name(),
                        ErrorMessageConstant.JOLT_TRANSFORMATION_FAILED);
            }
            return chainr;
        } catch (Exception e) {
            throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name() ,ErrorMessageConstant.JOLT_TRANSFORMATION_FAILED + e.getMessage());
        }
    }

    private String performJoltTransformation(Chainr chainr, BOLDocumentDataDTO documentData, String tripCode) {
        try {
            String documentDataJson = JsonUtils.toPrettyJson(documentData);
            Object documentDataObject = JsonUtils.fromJson(documentDataJson, Object.class);

            Object result = chainr.transform(documentDataObject);

            if (result == null) {
                result = new HashMap<>();
            }

            return JsonUtils.toJson(result);

        } catch (Exception e) {
            throw new TMSException(ERR_REQUEST_NOT_PROCESSED.name(),
                    String.format(ErrorMessageConstant.JOLT_TRANSFORMATION_FAILED, tripCode, e.getMessage()));
        }
    }

    private String handleJsonGenerationError(Exception e, String tripCode, Vendor vendor) {
        log.error("Failed to generate JSON for tripCode: {}, vendor: {}", tripCode, vendor, e);

        throw switch (e) {
            case TMSException tmsException -> tmsException;
            case GenericException genericException -> genericException;
            case ExecutionException execException -> handleExecutionException(execException, tripCode);
            case InterruptedException interruptedException -> {
                Thread.currentThread().interrupt();
                yield new GenericException("Thread interrupted: " + interruptedException.getMessage());
            }
            default -> new GenericException(
                    String.format(ErrorMessageConstant.DOCUMENT_GENERATION_FAILED, tripCode), 
                    HttpStatus.INTERNAL_SERVER_ERROR, e);
        };
    }
    
    private RuntimeException handleExecutionException(ExecutionException e, String tripCode) {
        Throwable cause = e.getCause();
        return switch (cause) {
            case TMSException tmsException -> tmsException;
            case GenericException genericException -> genericException;
            case RuntimeException runtimeException -> runtimeException;
            case null, default -> new GenericException(
                    String.format(ErrorMessageConstant.DOCUMENT_GENERATION_FAILED, tripCode), 
                    HttpStatus.INTERNAL_SERVER_ERROR, cause);
        };
    }

    @Override
    public CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>>
    generateContext(
            String tripCode, Vendor vendor) {
        log.info("Starting context generation for tripCode: {}, vendor: {}", tripCode, vendor);
        
        return buildDocumentData(tripCode)
                .thenApply(data -> {
                    DocumentGenerationContextDTO<BOLDocumentDataDTO> context = DocumentGenerationContextDTO.<BOLDocumentDataDTO>builder()
                            .vendor(vendor)
                            .documentType(getDocumentType())
                            .documentData(data)
                            .build();
                    log.info("Completed context generation for tripCode: {}", tripCode);
                    return context;
                });
    }

    private CompletableFuture<BOLDocumentDataDTO> buildDocumentData(String tripCode) {
        return CompletableFuture.supplyAsync(() -> fetchTrip(tripCode), executor)
                .thenCompose(trip -> {
                    BOLDocumentDataDTO.BOLDocumentDataDTOBuilder builder = initBuilder(trip);
                    BOLDocumentDataDTO tempData = builder.build();
                    
                    List<String> externalConsignmentIds = tempData.getExternalConsignmentIds();
                    List<String> externalFacilityCodes = tempData.getExternalFacilityCodes();
                    
                    CompletableFuture<Map<String, ConsignmentDetailsDTO>> consFuture = fetchConsignmentList(externalConsignmentIds);
                    CompletableFuture<Map<String, FacilityDetailsDTO>> facFuture = fetchFacilityList(externalFacilityCodes);
                    
                    return consFuture.thenCombine(facFuture, (consMap, facMap) -> tempData.toBuilder()
                            .consignmentDetailsMap(consMap)
                            .facilityDetailsMap(facMap)
                            .build());
                });
    }

    private Trip fetchTrip(String tripCode) {
        try {
            return tripDataService.getTripByCodeWithAllDetails(tripCode);
        } catch (TMSException e) {
            log.error("Failed to fetch trip with code: {}", tripCode, e);
            throw e;
        }
    }

    private BOLDocumentDataDTO.BOLDocumentDataDTOBuilder initBuilder(Trip trip) {
        List<Long> shipmentIds = trip.getShipments().stream()
                .map(Shipment::getId).distinct().toList();
        List<String> externalConsignmentIds = trip.getShipments().stream()
                .map(Shipment::getExternalConsignmentId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        List<String> externalFacilityCodes = Stream.concat(
                        trip.getStops().stream().map(Stop::getExternalLocationCode),
                        Stream.of(
                                trip.getExternalOriginLocationCode(),
                                trip.getExternalDestinationLocationCode()))
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        TripDocumentDTO tripDetails = TripDocumentDTO.builder()
                .tripCode(trip.getCode())
                .status(trip.getStatus())
                .externalOriginLocationCode(trip.getExternalOriginLocationCode())
                .externalDestinationLocationCode(trip.getExternalDestinationLocationCode())
                .expectedStartAt(DateTimeUtil.fromEpochMillis(trip.getExpectedStartAt()))
                .expectedEndAt(DateTimeUtil.fromEpochMillis(trip.getExpectedEndAt()))
                .actualStartAt(DateTimeUtil.fromEpochMillis(trip.getActualStartAt()))
                .actualEndAt(DateTimeUtil.fromEpochMillis(trip.getActualEndAt()))
                .build();

        return BOLDocumentDataDTO.builder()
                .shipmentIds(shipmentIds)
                .externalConsignmentIds(externalConsignmentIds)
                .externalFacilityCodes(externalFacilityCodes)
                .tripDetails(tripDetails);
    }

    private CompletableFuture<Map<String, ConsignmentDetailsDTO>> fetchConsignmentList(List<String> externalConsignmentIds) {
        return CompletableFuture.supplyAsync(() -> {
            if (externalConsignmentIds == null || externalConsignmentIds.isEmpty()) {
                return Collections.emptyMap();
            }
            
            OmsListResponse<ConsignmentRecord> consignmentResponse = omsServiceAdapter.getConsignmentList(
                    ConsignmentListRequestDTO.builder().ids(externalConsignmentIds).build(),
                    PaginationDTO.builder().pageNo(0).pageSize(externalConsignmentIds.size()).build());
            
            List<ConsignmentRecord> records = Optional.ofNullable(consignmentResponse.getResponse())
                    .orElse(Collections.emptyList());
            
            List<ConsignmentDetailsDTO> details = records.stream()
                    .map(consignmentMapper::mapToConsignmentDetails)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            enrichWithUomNames(details);
            
            Map<String, ConsignmentDetailsDTO> result = details.stream()
                    .collect(Collectors.toMap(
                            ConsignmentDetailsDTO::getConsignmentId,
                            Function.identity(),
                            (a, b) -> a));
            
            return result;
        }, executor);
    }

    private void enrichWithUomNames(List<ConsignmentDetailsDTO> details) {
        Set<Long> uomIds = details.stream()
                .flatMap(d -> Optional.ofNullable(d.getProductDetailsDTO().getProperties())
                        .stream().flatMap(map -> map.values().stream()))
                .map(ProductDetailsDTO.PropertyDetails::getResourceUomId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        if (uomIds.isEmpty()) {
            return;
        }
        
        ListResponse<UomRecord> uomResponse = resourceServiceAdapter.getUomList(
                UomListRequestDTO.builder()
                        .ids(uomIds.stream().map(Object::toString).collect(Collectors.toList()))
                        .codes(new ArrayList<>())
                        .build(),
                PaginationDTO.builder().pageNo(0).pageSize(uomIds.size()).build());
        
        List<UomRecord> records = Optional.ofNullable(uomResponse.getRecords())
                .orElse(Collections.emptyList());
        
        Map<Long, String> uomNames = records.stream()
                .collect(Collectors.toMap(UomRecord::getId, UomRecord::getName));
        
        details.forEach(d ->
                d.getProductDetailsDTO().getProperties().values().forEach(p ->
                        p.setResourceUomName(uomNames.get(p.getResourceUomId()))));
    }

    private CompletableFuture<Map<String, FacilityDetailsDTO>> fetchFacilityList(List<String> externalFacilityCodes) {
        return CompletableFuture.supplyAsync(() -> {
            ListResponse<FacilityRecord> facilityResponse = resourceServiceAdapter.getFacilityList(
                    FacilityListRequestDTO.builder()
                            .facilityCodes(externalFacilityCodes.stream().map(Objects::toString).toList())
                            .build(),
                    PaginationDTO.builder().pageNo(0).pageSize(externalFacilityCodes.size()).build());
            
            List<FacilityRecord> records = Optional.ofNullable(facilityResponse.getRecords())
                    .orElse(Collections.emptyList());
            
            Map<String, FacilityDetailsDTO> result = records.stream()
                    .map(facilityMapper::mapToFacilityDetails)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            facility -> facility.getId().toString(),
                            Function.identity(),
                            (a, b) -> a));
            
            log.info("Successfully fetched {} facilities", result.size());
            return result;
        }, executor);
    }
}
